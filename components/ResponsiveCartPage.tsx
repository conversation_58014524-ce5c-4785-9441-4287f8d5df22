'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import MobileCartPage from './mobile/MobileCartPage';
import CartContent from './CartContent';
import Navbar from './Navbar';

interface ResponsiveCartPageProps {
  locale: Locale;
}

const ResponsiveCartPage: React.FC<ResponsiveCartPageProps> = ({
  locale
}) => {
  return (
    <>
      {/* عرض النسخة المحمولة على الشاشات الصغيرة */}
      <div className="block md:hidden">
        <MobileCartPage locale={locale} />
      </div>

      {/* عرض النسخة المكتبية على الشاشات الكبيرة */}
      <div className="hidden md:block">
        <Navbar locale={locale} />
        <CartContent locale={locale} />
      </div>
    </>
  );
};

export default ResponsiveCartPage;
