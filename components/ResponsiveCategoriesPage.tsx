'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import MobileCategoriesPage from './mobile/MobileCategoriesPage';
import Navbar from './Navbar';
import Footer from './Footer';
import CategoriesPage from './CategoriesPage';
import dynamic from 'next/dynamic';

// تحميل ديناميكي للمكونات غير الضرورية للتحميل الأولي
const WhatsAppButton = dynamic(() => import('./WhatsAppButton'), {
  loading: () => null
});

// أنواع البيانات
interface Subcategory {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  product_count: number;
}

interface Category {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  subcategories: Subcategory[];
}

interface ResponsiveCategoriesPageProps {
  locale: Locale;
  categories?: Category[];
}

const ResponsiveCategoriesPage: React.FC<ResponsiveCategoriesPageProps> = ({
  locale,
  categories
}) => {
  return (
    <>
      {/* عرض النسخة المحمولة على الشاشات الصغيرة */}
      <div className="block md:hidden">
        <MobileCategoriesPage
          locale={locale}
          categories={categories}
        />
      </div>

      {/* عرض النسخة المكتبية على الشاشات الكبيرة */}
      <div className="hidden md:block">
        <Navbar locale={locale} />
        <main>
          <CategoriesPage locale={locale} />
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </div>
    </>
  );
};

export default ResponsiveCategoriesPage;
