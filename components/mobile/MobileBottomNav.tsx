'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Locale } from '../../lib/i18n';

interface MobileBottomNavProps {
  locale: Locale;
}

const MobileBottomNav: React.FC<MobileBottomNavProps> = ({ locale }) => {
  const pathname = usePathname();

  // تحديد الصفحة النشطة
  const isActive = (path: string) => {
    if (path === `/${locale}`) {
      return pathname === `/${locale}` || pathname === '/';
    }
    return pathname.startsWith(path);
  };

  const navItems = [
    {
      href: `/${locale}`,
      icon: 'ri-home-line',
      label: locale === 'ar' ? 'الرئيسية' : 'Home',
      id: 'home'
    },
    {
      href: `/${locale}/categories`,
      icon: 'ri-grid-line',
      label: locale === 'ar' ? 'الفئات' : 'Categories',
      id: 'categories'
    },
    {
      href: `/${locale}/products`,
      icon: 'ri-product-hunt-line',
      label: locale === 'ar' ? 'المنتجات' : 'Products',
      id: 'products'
    },
    {
      href: `/${locale}/search`,
      icon: 'ri-search-line',
      label: locale === 'ar' ? 'البحث' : 'Search',
      id: 'search'
    },
    {
      href: `/${locale}/contact`,
      icon: 'ri-customer-service-line',
      label: locale === 'ar' ? 'اتصل بنا' : 'Contact',
      id: 'contact'
    }
  ];

  return (
    <>
      {/* Fixed Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 safe-area-pb z-40">
        <div className="flex items-center justify-around">
          {navItems.map((item) => (
            <Link
              key={item.id}
              href={item.href}
              className={`flex flex-col items-center py-2 px-3 relative transition-colors ${
                isActive(item.href.split('?')[0]) ? 'text-primary' : 'text-gray-600'
              }`}
            >
              <i className={`${item.icon} text-xl mb-1`}></i>
              <span className="text-xs font-medium">
                {item.label}
              </span>
            </Link>
          ))}
        </div>
      </div>

      {/* Bottom Padding for Fixed Navigation */}
      <div className="h-20"></div>
    </>
  );
};

export default MobileBottomNav;
