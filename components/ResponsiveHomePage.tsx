'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import { Category, ProductWithDetails } from '../types/mysql-database';
import MobileHomePage from './mobile/MobileHomePage';
import Navbar from './Navbar';
import Footer from './Footer';
import HeroSection from './HeroSection';
import FeaturedProducts from './FeaturedProducts';
import CategoriesSection from './CategoriesSection';
import ServicesSection from './ServicesSection';
import PartnersSection from './PartnersSection';
import AboutSection from './AboutSection';
import dynamic from 'next/dynamic';

// تحميل ديناميكي للمكونات غير الضرورية للتحميل الأولي
const WhatsAppButton = dynamic(() => import('./WhatsAppButton'), {
  loading: () => null
});

interface ResponsiveHomePageProps {
  locale: Locale;
  categories?: Category[];
  featuredProducts?: ProductWithDetails[];
}

const ResponsiveHomePage: React.FC<ResponsiveHomePageProps> = ({
  locale,
  categories,
  featuredProducts
}) => {
  return (
    <>
      {/* عرض النسخة المحمولة على الشاشات الصغيرة */}
      <div className="block md:hidden">
        <MobileHomePage
          locale={locale}
          categories={categories}
          featuredProducts={featuredProducts}
        />
      </div>

      {/* عرض النسخة المكتبية على الشاشات الكبيرة */}
      <div className="hidden md:block">
        <Navbar locale={locale} />
        <main>
          <HeroSection locale={locale} />
          <ServicesSection locale={locale} />
          <CategoriesSection locale={locale} categories={categories} />
          <FeaturedProducts locale={locale} products={featuredProducts} />
          <AboutSection locale={locale} />
          <PartnersSection locale={locale} />
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </div>
    </>
  );
};

export default ResponsiveHomePage;
