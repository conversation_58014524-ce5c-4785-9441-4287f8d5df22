'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { Locale } from '../lib/i18n';
import { Category, Subcategory } from '../types/mysql-database';
import MobileCategoryPage from './mobile/MobileCategoryPage';
import Navbar from './Navbar';
import Footer from './Footer';
import dynamic from 'next/dynamic';

// تحميل ديناميكي للمكونات غير الضرورية للتحميل الأولي
const WhatsAppButton = dynamic(() => import('./WhatsAppButton'), {
  loading: () => null
});

interface ResponsiveCategoryPageProps {
  locale: Locale;
  categoryId: string;
  initialCategory?: Category | null;
  initialSubcategories?: Subcategory[];
}

const ResponsiveCategoryPage: React.FC<ResponsiveCategoryPageProps> = ({
  locale,
  categoryId,
  initialCategory,
  initialSubcategories
}) => {
  return (
    <>
      {/* عرض النسخة المحمولة على الشاشات الصغيرة */}
      <div className="block md:hidden">
        <MobileCategoryPage
          locale={locale}
          categoryId={categoryId}
          initialCategory={initialCategory}
          initialSubcategories={initialSubcategories}
        />
      </div>

      {/* عرض النسخة المكتبية على الشاشات الكبيرة */}
      <div className="hidden md:block">
        <Navbar locale={locale} />
        <DesktopCategoryContent
          locale={locale}
          categoryId={categoryId}
          initialCategory={initialCategory}
          initialSubcategories={initialSubcategories}
        />
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </div>
    </>
  );
};

// مكون المحتوى للنسخة المكتبية
interface DesktopCategoryContentProps {
  locale: Locale;
  categoryId: string;
  initialCategory?: Category | null;
  initialSubcategories?: Subcategory[];
}

const DesktopCategoryContent: React.FC<DesktopCategoryContentProps> = ({
  locale,
  categoryId,
  initialCategory,
  initialSubcategories
}) => {
  const [category, setCategory] = useState<Category | null>(initialCategory || null);
  const [subcategories, setSubcategories] = useState<Subcategory[]>(initialSubcategories || []);
  const [loading, setLoading] = useState(!initialCategory);

  const fetchCategoryData = useCallback(async () => {
    try {
      setLoading(true);

      // جلب بيانات الفئة
      const categoryResponse = await fetch(`/api/categories?id=${categoryId}`);
      if (categoryResponse.ok) {
        const categoryResult = await categoryResponse.json();
        if (categoryResult.success && categoryResult.data) {
          setCategory(categoryResult.data);
        }
      }

      // جلب الفئات الفرعية
      const subcategoriesResponse = await fetch(`/api/subcategories?categoryId=${categoryId}`);
      if (subcategoriesResponse.ok) {
        const subcategoriesResult = await subcategoriesResponse.json();
        if (subcategoriesResult.success && subcategoriesResult.data) {
          setSubcategories(subcategoriesResult.data);
        }
      }
    } catch (error) {
      console.error('Error fetching category data:', error);
    } finally {
      setLoading(false);
    }
  }, [categoryId]);

  useEffect(() => {
    if (!initialCategory && categoryId) {
      fetchCategoryData();
    }
  }, [categoryId, initialCategory, fetchCategoryData]);

  if (loading) {
    return (
      <main className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">
            {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </main>
    );
  }

  if (!category) {
    return (
      <main className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <i className="ri-error-warning-line text-6xl text-red-400 mb-4"></i>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">
            {locale === 'ar' ? 'الفئة غير موجودة' : 'Category not found'}
          </h3>
          <p className="text-gray-600 mb-6">
            {locale === 'ar'
              ? 'لم يتم العثور على هذه الفئة'
              : 'This category could not be found'
            }
          </p>
          <a
            href={`/${locale}/categories`}
            className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
          >
            <i className="ri-arrow-left-line"></i>
            <span>{locale === 'ar' ? 'العودة للفئات' : 'Back to Categories'}</span>
          </a>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <section className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-700 py-16">
        <div className="container mx-auto px-4">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-white/80 mb-6">
            <a href={`/${locale}`} className="hover:text-white transition-colors">
              {locale === 'ar' ? 'الرئيسية' : 'Home'}
            </a>
            <span>/</span>
            <a href={`/${locale}/categories`} className="hover:text-white transition-colors">
              {locale === 'ar' ? 'الفئات' : 'Categories'}
            </a>
            <span>/</span>
            <span className="text-white font-medium">
              {locale === 'ar' ? category.name_ar : category.name}
            </span>
          </nav>
          
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {locale === 'ar' ? category.name_ar : category.name}
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto mb-6">
              {locale === 'ar' ? category.description_ar : category.description}
            </p>
            <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse text-white/80">
              <span className="flex items-center space-x-2 rtl:space-x-reverse">
                <i className="ri-folder-line"></i>
                <span>{subcategories.length} {locale === 'ar' ? 'فئة فرعية' : 'Subcategories'}</span>
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Subcategories Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              {locale === 'ar' ? 'الفئات الفرعية' : 'Subcategories'}
            </h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              {locale === 'ar'
                ? 'اختر الفئة الفرعية لعرض المنتجات المتعلقة بها'
                : 'Choose a subcategory to view related products'
              }
            </p>
          </div>

          {/* Subcategories Grid */}
          {subcategories.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {subcategories.map((subcategory) => (
                <a
                  key={subcategory.id}
                  href={`/${locale}/subcategory/${subcategory.id}`}
                  className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                >
                  <div className="aspect-square bg-gray-200 overflow-hidden relative">
                    {subcategory.image_url ? (
                      <Image
                        src={subcategory.image_url}
                        alt={locale === 'ar' ? subcategory.name_ar : subcategory.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-300"
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                        <i className="ri-folder-line text-4xl text-gray-400"></i>
                      </div>
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-primary transition-colors duration-300">
                      {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                    </h3>
                    {subcategory.description_ar && (
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {locale === 'ar' ? subcategory.description_ar : subcategory.description}
                      </p>
                    )}
                    <div className="mt-4 flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        {locale === 'ar' ? 'عرض المنتجات' : 'View Products'}
                      </span>
                      <div className="bg-primary/10 rounded-full p-2 group-hover:bg-primary group-hover:text-white transition-all duration-300">
                        <i className="ri-arrow-right-line text-primary group-hover:text-white"></i>
                      </div>
                    </div>
                  </div>
                </a>
              ))}
            </div>
          ) : (
            <div className="text-center py-16 bg-white rounded-2xl shadow-lg">
              <i className="ri-folder-line text-6xl text-gray-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                {locale === 'ar' ? 'لا توجد فئات فرعية' : 'No subcategories found'}
              </h3>
              <p className="text-gray-600 mb-6">
                {locale === 'ar'
                  ? 'هذه الفئة لا تحتوي على فئات فرعية.'
                  : 'This category has no subcategories.'
                }
              </p>
            </div>
          )}
        </div>
      </section>
    </main>
  );
};

export default ResponsiveCategoryPage;
