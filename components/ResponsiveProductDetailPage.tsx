'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import { ProductWithDetails, Category, Subcategory } from '../types/mysql-database';
import MobileProductDetailPage from './mobile/MobileProductDetailPage';
import ProductPageClient from '../app/[locale]/product/[id]/ProductPageClient';
import Navbar from './Navbar';
import Footer from './Footer';

interface ResponsiveProductDetailPageProps {
  locale: Locale;
  initialProduct?: ProductWithDetails | null;
  initialCategory?: Category | null;
  initialSubcategory?: Subcategory | null;
  productId: string;
}

const ResponsiveProductDetailPage: React.FC<ResponsiveProductDetailPageProps> = ({
  locale,
  initialProduct,
  initialCategory,
  initialSubcategory,
  productId
}) => {
  return (
    <>
      {/* عرض النسخة المحمولة على الشاشات الصغيرة */}
      <div className="block md:hidden">
        <MobileProductDetailPage
          locale={locale}
          initialProduct={initialProduct}
          initialCategory={initialCategory}
          initialSubcategory={initialSubcategory}
          productId={productId}
        />
      </div>

      {/* عرض النسخة المكتبية على الشاشات الكبيرة */}
      <div className="hidden md:block">
        <Navbar locale={locale} />
        <ProductPageClient
          initialProduct={initialProduct || null}
          initialCategory={initialCategory || null}
          initialSubcategory={initialSubcategory || null}
          locale={locale}
          productId={productId}
        />
        <Footer locale={locale} />
      </div>
    </>
  );
};

export default ResponsiveProductDetailPage;
