'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import { Category, ProductWithDetails } from '../types/mysql-database';
import MobileProductsPage from './mobile/MobileProductsPage';
import ProductsContent from './ProductsContent';
import Navbar from './Navbar';
import Footer from './Footer';
import WhatsAppButton from './WhatsAppButton';

interface ResponsiveProductsPageProps {
  locale: Locale;
  initialProducts?: ProductWithDetails[];
  initialCategories?: Category[];
}

const ResponsiveProductsPage: React.FC<ResponsiveProductsPageProps> = ({
  locale,
  initialProducts,
  initialCategories
}) => {
  return (
    <>
      {/* عرض النسخة المحمولة على الشاشات الصغيرة */}
      <div className="block md:hidden">
        <MobileProductsPage
          locale={locale}
          initialProducts={initialProducts}
          initialCategories={initialCategories}
        />
      </div>

      {/* عرض النسخة المكتبية على الشاشات الكبيرة */}
      <div className="hidden md:block">
        <Navbar locale={locale} />
        <ProductsContent
          locale={locale}
          initialProducts={initialProducts}
          initialCategories={initialCategories}
        />
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </div>
    </>
  );
};

export default ResponsiveProductsPage;
