import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../lib/i18n';
import { ProductWithDetails } from '../types/mysql-database';
import { generateProductUrl } from '../utils/generateSlug';

interface ServerProductCardProps {
  product: ProductWithDetails;
  locale: Locale;
}

const ServerProductCard: React.FC<ServerProductCardProps> = ({
  product,
  locale
}) => {
  const productTitle = locale === 'ar' ? product.title_ar : product.title;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;
  
  // إنشاء رابط المنتج
  const productUrl = generateProductUrl(product, locale);
  
  // الحصول على أول صورة للمنتج
  const productImage = product.images && product.images.length > 0 
    ? product.images[0].image_url 
    : '/api/placeholder?width=300&height=300&text=No+Image';

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      <Link href={productUrl} className="block">
        {/* Product Image */}
        <div className="aspect-square relative bg-gray-100">
          <Image
            src={productImage}
            alt={productTitle}
            fill
            className="object-cover transition-transform duration-300 hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
          />
          
          {/* Availability Badge */}
          <div className="absolute top-3 right-3">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              product.is_available 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {product.is_available 
                ? (locale === 'ar' ? 'متوفر' : 'Available')
                : (locale === 'ar' ? 'غير متوفر' : 'Out of Stock')
              }
            </span>
          </div>
        </div>

        {/* Product Info */}
        <div className="p-4">
          <h3 className="font-bold text-gray-800 mb-2 line-clamp-2 min-h-[3rem]">
            {productTitle}
          </h3>
          
          {productDescription && (
            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
              {productDescription.length > 100 
                ? productDescription.substring(0, 100) + '...'
                : productDescription
              }
            </p>
          )}

          {/* Price */}
          {product.price && product.price > 0 && (
            <div className="text-lg font-bold text-primary mb-3">
              {product.price} {locale === 'ar' ? 'ريال' : 'SAR'}
            </div>
          )}

          {/* Product Features Preview */}
          {product.features && product.features.length > 0 && (
            <div className="mb-3">
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <i className="ri-check-line text-green-500"></i>
                <span>
                  {locale === 'ar' ? product.features[0].feature_text_ar : product.features[0].feature_text}
                </span>
              </div>
              {product.features.length > 1 && (
                <div className="text-xs text-gray-400 mt-1">
                  +{product.features.length - 1} {locale === 'ar' ? 'مميزات أخرى' : 'more features'}
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <div className="flex-1">
              <span className="w-full bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-lg font-semibold text-center text-sm transition-colors inline-block">
                {locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}
              </span>
            </div>
            
            <Link
              href={`/${locale}/contact?product=${product.id}`}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 rounded-lg transition-colors"
              title={locale === 'ar' ? 'استفسار سريع' : 'Quick Inquiry'}
            >
              <i className="ri-whatsapp-line text-lg"></i>
            </Link>
          </div>

          {/* Product ID */}
          <div className="mt-2 text-xs text-gray-400">
            {locale === 'ar' ? 'رقم المنتج:' : 'SKU:'} DH-{product.id}
          </div>
        </div>
      </Link>
    </div>
  );
};

export default ServerProductCard;
