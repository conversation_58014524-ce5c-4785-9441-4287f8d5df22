'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../lib/i18n';
import { ProductWithDetails } from '../types/mysql-database';
import { generateProductUrl } from '../utils/generateSlug';
// تم حذف استيراد getTranslation لأنه غير مستخدم في هذا المكون

interface SimilarProductsProps {
  locale: Locale;
  currentProductId: string;
  categoryId?: string;
  subcategoryId?: string;
  limit?: number;
}

const SimilarProducts: React.FC<SimilarProductsProps> = ({
  locale,
  currentProductId,
  categoryId,
  subcategoryId,
  limit = 6
}) => {
  const [products, setProducts] = useState<ProductWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSimilarProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // بناء URL للبحث عن المنتجات المشابهة
        let apiUrl = '/api/products?';
        const params = new URLSearchParams();

        // إعطاء أولوية للفئة الفرعية، ثم الفئة الرئيسية
        if (subcategoryId) {
          params.append('subcategoryId', subcategoryId);
        } else if (categoryId) {
          params.append('categoryId', categoryId);
        }

        params.append('limit', (limit + 1).toString()); // +1 لاستبعاد المنتج الحالي
        apiUrl += params.toString();

        const response = await fetch(apiUrl);
        if (!response.ok) {
          throw new Error('Failed to fetch similar products');
        }

        const result = await response.json();
        if (result.success && result.data) {
          // استبعاد المنتج الحالي وأخذ العدد المطلوب
          const filteredProducts = result.data
            .filter((product: ProductWithDetails) => product.id !== currentProductId)
            .slice(0, limit);

          setProducts(filteredProducts);
        }
      } catch (err) {
        console.error('Error fetching similar products:', err);
        setError('Failed to load similar products');
      } finally {
        setLoading(false);
      }
    };

    if (categoryId || subcategoryId) {
      fetchSimilarProducts();
    } else {
      setLoading(false);
    }
  }, [currentProductId, categoryId, subcategoryId, limit]);

  if (loading) {
    return (
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">
              {locale === 'ar' ? 'جاري تحميل المنتجات المشابهة...' : 'Loading similar products...'}
            </p>
          </div>
        </div>
      </section>
    );
  }

  if (error || products.length === 0) {
    return null; // لا نعرض شيئاً إذا لم توجد منتجات أو حدث خطأ
  }

  return (
    <section className="py-8 lg:py-12">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
            {locale === 'ar' ? 'منتجات مشابهة' : 'Similar Products'}
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {locale === 'ar'
              ? 'اكتشف المزيد من المنتجات المشابهة التي قد تهمك'
              : 'Discover more similar products that might interest you'
            }
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product) => (
            <SimilarProductCard
              key={product.id}
              product={product}
              locale={locale}
            />
          ))}
        </div>

        {/* View More Link for SEO */}
        {categoryId && (
          <div className="text-center mt-8">
            <Link
              href={`/${locale}/categories/${categoryId}`}
              className="inline-flex items-center px-6 py-3 bg-primary text-white font-semibold rounded-lg hover:bg-primary-dark transition-colors"
            >
              {locale === 'ar' ? 'عرض المزيد من المنتجات' : 'View More Products'}
              <i className={`ri-arrow-${locale === 'ar' ? 'left' : 'right'}-line ml-2`}></i>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

// مكون بطاقة المنتج المشابه
interface SimilarProductCardProps {
  product: ProductWithDetails;
  locale: Locale;
}

const SimilarProductCard: React.FC<SimilarProductCardProps> = ({ product, locale }) => {
  const productUrl = generateProductUrl(product, locale);
  const productTitle = locale === 'ar' ? product.title_ar : product.title;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;

  return (
    <Link href={`/${locale}${productUrl}`} className="group">
      <div className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100 hover:border-primary/20">
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden">
          <Image
            src={product.images?.[0]?.image_url || '/placeholder-image.jpg'}
            alt={`${productTitle} - ${locale === 'ar' ? 'معدات فنادق احترافية' : 'Professional hotel equipment'}`}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
          />
          
          {/* Price Badge */}
          {product.price && (
            <div className="absolute top-3 right-3 bg-primary text-white px-2 py-1 rounded-lg text-sm font-semibold">
              {product.price} {locale === 'ar' ? 'ريال' : 'SAR'}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="p-4">
          <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary transition-colors">
            {productTitle}
          </h3>
          
          {productDescription && (
            <p className="text-gray-600 text-sm line-clamp-2 mb-3">
              {productDescription}
            </p>
          )}

          {/* Action Button */}
          <div className="flex items-center justify-between">
            <span className="text-primary font-semibold text-sm">
              {locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}
            </span>
            <i className={`ri-arrow-${locale === 'ar' ? 'left' : 'right'}-line text-primary group-hover:translate-x-1 transition-transform`}></i>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default SimilarProducts;
