'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import MobileContactPage from './mobile/MobileContactPage';
import ContactContent from './ContactContent';
import Navbar from './Navbar';

interface ResponsiveContactPageProps {
  locale: Locale;
}

const ResponsiveContactPage: React.FC<ResponsiveContactPageProps> = ({
  locale
}) => {
  return (
    <>
      {/* عرض النسخة المحمولة على الشاشات الصغيرة */}
      <div className="block md:hidden">
        <MobileContactPage locale={locale} />
      </div>

      {/* عرض النسخة المكتبية على الشاشات الكبيرة */}
      <div className="hidden md:block">
        <Navbar locale={locale} />
        <ContactContent locale={locale} />
      </div>
    </>
  );
};

export default ResponsiveContactPage;
