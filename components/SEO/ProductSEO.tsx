import { Metadata } from 'next';
import { Locale } from '@/lib/i18n';
import { getPageKeywords } from '@/lib/main-keywords';
import { generateProductUrl } from '@/utils/generateSlug';

interface Product {
  id: number | string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  category?: {
    name: string;
    name_ar: string;
  };
  subcategory?: {
    name: string;
    name_ar: string;
  };
}

// تم نقل ProductSEOProps إلى EnhancedProductSEOProps أدناه

export function generateProductMetadata(
  product: Product,
  locale: Locale
): Metadata {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const productName = locale === 'ar' ? product.name_ar : product.name;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;
  const categoryName = locale === 'ar' ? product.category?.name_ar : product.category?.name;
  
  // إنشاء العنوان
  const title = locale === 'ar'
    ? `${productName} - معدات فندقية احترافية عالية الجودة | ${siteName}`
    : `${productName} - Professional High Quality Hotel Equipment | ${siteName}`;

  // إنشاء الوصف
  const description = locale === 'ar'
    ? `${productName} من ${siteName} - ${productDescription || 'معدات فندقية احترافية عالية الجودة'}. ${categoryName ? `ضمن فئة ${categoryName}` : ''}. عروض أسعار مخصصة ومورد موثوق للفنادق.`
    : `${productName} from ${siteName} - ${productDescription || 'Professional high quality hotel equipment'}. ${categoryName ? `In ${categoryName} category` : ''}. Custom quotes and trusted hotel supplier.`;

  // إنشاء الكلمات المفتاحية
  const baseKeywords = getPageKeywords('products', locale);
  const productSpecificKeywords = locale === 'ar' 
    ? [
        productName,
        `${productName} فندقي`,
        `${productName} للفنادق`,
        categoryName ? `${categoryName} فندقية` : '',
        'تجهيزات فندقية',
        'مستلزمات الفنادق',
        'معدات الفنادق',
        'مورد تجهيزات فنادق',
        'عرض سعر فندقي'
      ].filter(Boolean)
    : [
        productName,
        `hotel ${productName.toLowerCase()}`,
        `${productName.toLowerCase()} for hotels`,
        categoryName ? `hotel ${categoryName.toLowerCase()}` : '',
        'hotel equipment',
        'hotel supplies',
        'hospitality supplies',
        'hotel supply supplier',
        'hotel equipment quotation'
      ].filter(Boolean);

  const keywords = [...baseKeywords, ...productSpecificKeywords].join(', ');

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url: `${baseUrl}/${locale}/product/${product.id}`,
      siteName,
      type: 'website',
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/product/${product.id}`,
      languages: {
        'ar': `${baseUrl}/ar/product/${product.id}`,
        'en': `${baseUrl}/en/product/${product.id}`,
      },
    },
  };
}

// مكون محسن لـ JSON-LD للمنتج مع جميع البيانات المطلوبة
interface EnhancedProductSEOProps {
  product: Product & {
    price?: number;
    images?: Array<{ image_url: string }>;
    specifications?: string;
    sku?: string;
    is_available?: boolean;
  };
  locale: Locale;
}

export function ProductJsonLd({ product, locale }: EnhancedProductSEOProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';

  const productName = locale === 'ar' ? product.name_ar : product.name;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;
  const categoryName = product.category ? (locale === 'ar' ? product.category.name_ar : product.category.name) : undefined;

  // إنشاء URL صحيح للمنتج باستخدام slug
  const productUrl = generateProductUrl({
    id: typeof product.id === 'string' ? product.id : product.id.toString(),
    title: productName || 'product',
    title_ar: product.name_ar || productName || 'منتج'
  }, locale);
  const fullProductUrl = `${baseUrl}/${locale}${productUrl}`;

  // إنشاء مصفوفة الصور مع البيانات الوصفية
  const productImages = product.images && product.images.length > 0
    ? product.images.map(img => ({
        '@type': 'ImageObject',
        url: img.image_url.startsWith('http') ? img.image_url : `${baseUrl}${img.image_url}`,
        width: 800,
        height: 600,
        caption: productName
      }))
    : [{
        '@type': 'ImageObject',
        url: `${baseUrl}/placeholder-image.jpg`,
        width: 800,
        height: 600,
        caption: productName
      }];

  // إنشاء بيانات التقييم المحسنة (يمكن تحديثها لاحقاً من قاعدة البيانات)
  // تم تعطيل aggregateRating مؤقتاً لأنه يتطلب مراجعات حقيقية
  // const aggregateRating = {
  //   '@type': 'AggregateRating',
  //   ratingValue: '4.7',
  //   reviewCount: '25',
  //   bestRating: '5',
  //   worstRating: '1'
  // };

  // إنشاء بيانات العرض المحسنة
  const offer = {
    '@type': 'Offer',
    url: fullProductUrl,
    priceCurrency: 'SAR',
    price: String(product.price || 0),
    priceValidUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // سنة من الآن
    availability: product.is_available !== false ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
    itemCondition: 'https://schema.org/NewCondition',
    hasMerchantReturnPolicy: {
      '@type': 'MerchantReturnPolicy',
      applicableCountry: 'SA',
      returnPolicyCategory: 'https://schema.org/MerchantReturnFiniteReturnWindow',
      merchantReturnDays: 30,
      returnMethod: 'https://schema.org/ReturnByMail'
    },
    shippingDetails: {
      '@type': 'OfferShippingDetails',
      shippingRate: {
        '@type': 'MonetaryAmount',
        value: '0',
        currency: 'SAR'
      },
      deliveryTime: {
        '@type': 'ShippingDeliveryTime',
        handlingTime: {
          '@type': 'QuantitativeValue',
          minValue: 1,
          maxValue: 3,
          unitCode: 'DAY'
        },
        transitTime: {
          '@type': 'QuantitativeValue',
          minValue: 2,
          maxValue: 7,
          unitCode: 'DAY'
        }
      }
    },
    seller: {
      '@type': 'Organization',
      name: siteName,
      url: baseUrl,
      telephone: '+966599252259',
      address: {
        '@type': 'PostalAddress',
        addressCountry: 'SA',
        addressLocality: locale === 'ar' ? 'الرياض' : 'Riyadh'
      }
    }
  };

  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    '@id': `${fullProductUrl}#product`,
    name: productName || (locale === 'ar' ? 'منتج فندقي' : 'Hotel Product'),
    description: productDescription || (locale === 'ar'
      ? 'تجهيزات فندقية عالية الجودة من دروب هجر - معدات احترافية للفنادق والمطاعم'
      : 'High quality hotel equipment from DROOB HAJER - Professional equipment for hotels and restaurants'
    ),
    image: productImages,
    brand: {
      '@type': 'Brand',
      name: siteName,
      url: baseUrl
    },
    manufacturer: {
      '@type': 'Organization',
      name: siteName,
      url: baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/favicon-192x192.png`,
        width: 192,
        height: 192
      }
    },
    category: categoryName || 'Hotel Equipment',
    sku: product.sku || `DH-${product.id}`,
    mpn: product.sku || `DH-${product.id}`,
    identifier: {
      '@type': 'PropertyValue',
      name: 'SKU',
      value: product.sku || `DH-${product.id}`
    },
    url: fullProductUrl,
    offers: offer,
    // aggregateRating: aggregateRating, // تم تعطيله مؤقتاً
    additionalProperty: product.specifications ? [
      {
        '@type': 'PropertyValue',
        name: locale === 'ar' ? 'المواصفات التقنية' : 'Technical Specifications',
        value: product.specifications
      },
      {
        '@type': 'PropertyValue',
        name: locale === 'ar' ? 'الاستخدام' : 'Usage',
        value: locale === 'ar' ? 'مناسب للفنادق والمطاعم والمقاهي' : 'Suitable for hotels, restaurants and cafes'
      },
      {
        '@type': 'PropertyValue',
        name: locale === 'ar' ? 'الضمان' : 'Warranty',
        value: locale === 'ar' ? 'ضمان الجودة' : 'Quality guarantee'
      }
    ] : [
      {
        '@type': 'PropertyValue',
        name: locale === 'ar' ? 'الاستخدام' : 'Usage',
        value: locale === 'ar' ? 'مناسب للفنادق والمطاعم والمقاهي' : 'Suitable for hotels, restaurants and cafes'
      },
      {
        '@type': 'PropertyValue',
        name: locale === 'ar' ? 'الضمان' : 'Warranty',
        value: locale === 'ar' ? 'ضمان الجودة' : 'Quality guarantee'
      }
    ],
    // ربط المنتج بالفئة باستخدام Thing بدلاً من ProductGroup
    isRelatedTo: categoryName && product.category ? [
      {
        '@type': 'Thing',
        '@id': `${baseUrl}/${locale}/categories/${product.category.name}`,
        name: categoryName,
        url: `${baseUrl}/${locale}/categories/${product.category.name}`
      }
    ] : undefined,
    potentialAction: {
      '@type': 'BuyAction',
      target: fullProductUrl,
      price: String(product.price || 0),
      priceCurrency: 'SAR'
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd, null, 2) }}
    />
  );
}

// دالة لإنشاء كلمات مفتاحية خاصة بالمنتج
export function generateProductKeywords(
  product: Product,
  locale: Locale
): string[] {
  const productName = locale === 'ar' ? product.name_ar : product.name;
  const categoryName = locale === 'ar' ? product.category?.name_ar : product.category?.name;
  const subcategoryName = locale === 'ar' ? product.subcategory?.name_ar : product.subcategory?.name;

  const baseKeywords = locale === 'ar'
    ? ['تجهيزات فندقية', 'مستلزمات الفنادق', 'معدات الفنادق', 'مورد تجهيزات فنادق']
    : ['hotel equipment', 'hotel supplies', 'hospitality supplies', 'hotel supply supplier'];

  const productKeywords = [
    productName,
    categoryName,
    subcategoryName,
    ...(locale === 'ar'
      ? [
          `${productName} فندقي`,
          `${productName} للفنادق`,
          categoryName ? `${categoryName} فندقية` : '',
          subcategoryName ? `${subcategoryName} فندقية` : ''
        ]
      : [
          `hotel ${productName.toLowerCase()}`,
          `${productName.toLowerCase()} for hotels`,
          categoryName ? `hotel ${categoryName.toLowerCase()}` : '',
          subcategoryName ? `hotel ${subcategoryName.toLowerCase()}` : ''
        ]
    )
  ].filter((keyword): keyword is string => Boolean(keyword));

  return [...baseKeywords, ...productKeywords];
}
