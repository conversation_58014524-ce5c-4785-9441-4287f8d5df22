'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import MobileAboutPage from './mobile/MobileAboutPage';
import AboutContent from './AboutContent';
import Navbar from './Navbar';

interface ResponsiveAboutPageProps {
  locale: Locale;
}

const ResponsiveAboutPage: React.FC<ResponsiveAboutPageProps> = ({
  locale
}) => {
  return (
    <>
      {/* عرض النسخة المحمولة على الشاشات الصغيرة */}
      <div className="block md:hidden">
        <MobileAboutPage locale={locale} />
      </div>

      {/* عرض النسخة المكتبية على الشاشات الكبيرة */}
      <div className="hidden md:block">
        <Navbar locale={locale} />
        <AboutContent locale={locale} />
      </div>
    </>
  );
};

export default ResponsiveAboutPage;
