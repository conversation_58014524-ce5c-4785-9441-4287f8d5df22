// mysql2 will be required dynamically inside the function

/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com',
  generateRobotsTxt: false, // نستخدم السكريبت المخصص لإنشاء robots.txt
  generateIndexSitemap: false,
  exclude: [
    '/admin/*',
    '/api/*',
    '/cart',
    '/checkout',
    '/profile',
    '/login',
    '/register',
    '/404',
    '/500',
    '/server-sitemap-index.xml'
  ],
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/admin/',
          '/api/',
          '/cart',
          '/checkout',
          '/profile',
          '/login',
          '/register',
          '/_next/webpack-hmr',
          '/_next/server/',
          '*.json',
          '*.xml'
        ],
      },
      {
        userAgent: '*',
        allow: [
          '/_next/static/',
          '/_next/image',
          '/favicon.ico',
          '/favicon-*.png',
          '/apple-icon-*.png',
          '/android-icon-*.png',
          '/ms-icon-*.png',
          '/manifest.json',
          '/browserconfig.xml',
          '/sitemap.xml',
          '/robots.txt'
        ]
      }
    ],
    additionalSitemaps: [
      'https://droobhajer.com/sitemap.xml',
    ],
  },
  transform: async (config, path) => {
    // تخصيص الأولوية وتكرار التحديث حسب نوع الصفحة
    let priority = 0.7;
    let changefreq = 'weekly';

    // الصفحة الرئيسية
    if (path === '/ar' || path === '/en' || path === '/') {
      priority = 1.0;
      changefreq = 'daily';
    }
    // صفحات المنتجات
    else if (path.includes('/products')) {
      priority = 0.9;
      changefreq = 'daily';
    }
    // صفحات الفئات
    else if (path.includes('/categories')) {
      priority = 0.8;
      changefreq = 'weekly';
    }
    // صفحات ثابتة مهمة
    else if (path.includes('/about') || path.includes('/contact')) {
      priority = 0.6;
      changefreq = 'monthly';
    }

    return {
      loc: path,
      changefreq,
      priority,
      lastmod: new Date().toISOString(),
    };
  },
  additionalPaths: async () => {
    const currentTime = new Date().toISOString();
    const paths = [];

    // الصفحات الثابتة
    const staticPages = [
      { loc: '/ar', changefreq: 'daily', priority: 1.0 },
      { loc: '/en', changefreq: 'daily', priority: 1.0 },
      { loc: '/ar/products', changefreq: 'daily', priority: 0.9 },
      { loc: '/en/products', changefreq: 'daily', priority: 0.9 },
      { loc: '/ar/categories', changefreq: 'weekly', priority: 0.8 },
      { loc: '/en/categories', changefreq: 'weekly', priority: 0.8 },
      { loc: '/ar/about', changefreq: 'monthly', priority: 0.6 },
      { loc: '/en/about', changefreq: 'monthly', priority: 0.6 },
      { loc: '/ar/contact', changefreq: 'monthly', priority: 0.6 },
      { loc: '/en/contact', changefreq: 'monthly', priority: 0.6 },
    ];

    // إضافة الصفحات الثابتة
    staticPages.forEach(page => {
      paths.push({
        ...page,
        lastmod: currentTime,
      });
    });

    // استخدام اتصال مباشر بقاعدة البيانات
    try {
      const mysql = require('mysql2/promise');

      // إعدادات قاعدة البيانات
      const dbConfig = {
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'droobhajer_db',
        charset: 'utf8mb4'
      };

      console.log('🔄 Connecting to database for sitemap...');
      const connection = await mysql.createConnection(dbConfig);
      console.log('✅ Connected to database');

      // جلب المنتجات
      console.log('🔄 Fetching products for sitemap...');
      const [products] = await connection.execute(
        'SELECT id, title, title_ar, updated_at FROM products WHERE deleted_at IS NULL AND is_active = 1 ORDER BY id'
      );
      console.log(`✅ Found ${products.length} products for sitemap`);

      products.forEach(product => {
        const lastmod = product.updated_at ? new Date(product.updated_at).toISOString() : currentTime;

        // إنشاء slug من عنوان المنتج
        const generateSlug = (text) => {
          if (!text) return '';
          return text
            .toLowerCase()
            .replace(/[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF-]/g, '')
            .replace(/\s+/g, ' ')
            .trim()
            .replace(/\s/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-+|-+$/g, '');
        };

        const arSlug = generateSlug(product.title_ar);
        const enSlug = generateSlug(product.title);

        paths.push({
          loc: `/ar/products/${arSlug}-${product.id}`,
          changefreq: 'weekly',
          priority: 0.8,
          lastmod: lastmod,
        });

        paths.push({
          loc: `/en/products/${enSlug}-${product.id}`,
          changefreq: 'weekly',
          priority: 0.8,
          lastmod: lastmod,
        });
      });

      // جلب الفئات
      console.log('🔄 Fetching categories for sitemap...');
      const [categories] = await connection.execute(
        'SELECT id, updated_at FROM categories WHERE deleted_at IS NULL AND is_active = 1 ORDER BY id'
      );
      console.log(`✅ Found ${categories.length} categories for sitemap`);

      categories.forEach(category => {
        const lastmod = category.updated_at ? new Date(category.updated_at).toISOString() : currentTime;

        paths.push({
          loc: `/ar/category/${category.id}`,
          changefreq: 'weekly',
          priority: 0.7,
          lastmod: lastmod,
        });

        paths.push({
          loc: `/en/category/${category.id}`,
          changefreq: 'weekly',
          priority: 0.7,
          lastmod: lastmod,
        });
      });

      // جلب الفئات الفرعية
      console.log('🔄 Fetching subcategories for sitemap...');
      const [subcategories] = await connection.execute(
        'SELECT id, updated_at FROM subcategories WHERE deleted_at IS NULL AND is_active = 1 ORDER BY id'
      );
      console.log(`✅ Found ${subcategories.length} subcategories for sitemap`);

      subcategories.forEach(subcategory => {
        const lastmod = subcategory.updated_at ? new Date(subcategory.updated_at).toISOString() : currentTime;

        paths.push({
          loc: `/ar/subcategory/${subcategory.id}`,
          changefreq: 'weekly',
          priority: 0.6,
          lastmod: lastmod,
        });

        paths.push({
          loc: `/en/subcategory/${subcategory.id}`,
          changefreq: 'weekly',
          priority: 0.6,
          lastmod: lastmod,
        });
      });

      await connection.end();
      console.log('🔌 Database connection closed');

    } catch (error) {
      console.error('❌ Error connecting to database for sitemap:', error);
    }

    console.log(`🎉 Generated ${paths.length} total paths for sitemap`);
    return paths;
  },
  autoLastmod: true,
  sitemapSize: 5000,
  outDir: './public',
};
