const https = require('https');

// اختبار Schema لصفحة منتج
function testProductSchema(url) {
  console.log(`🔍 اختبار Schema للصفحة: ${url}`);
  
  https.get(url, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        // البحث عن JSON-LD Schema
        const jsonLdMatch = data.match(/<script type="application\/ld\+json"[^>]*>(.*?)<\/script>/s);
        
        if (jsonLdMatch) {
          const schemaData = JSON.parse(jsonLdMatch[1]);
          console.log('✅ تم العثور على Schema:');
          console.log('📋 نوع Schema:', schemaData['@type']);
          console.log('🏷️ اسم المنتج:', schemaData.name);
          console.log('🔗 URL المنتج:', schemaData.url);
          console.log('💰 السعر:', schemaData.offers?.price);
          console.log('🏢 العلامة التجارية:', schemaData.brand?.name);
          
          // فحص المشاكل المحتملة
          const issues = [];
          
          if (!schemaData.name) issues.push('❌ اسم المنتج مفقود');
          if (!schemaData.description) issues.push('❌ وصف المنتج مفقود');
          if (!schemaData.image || schemaData.image.length === 0) issues.push('❌ صور المنتج مفقودة');
          if (!schemaData.offers) issues.push('❌ بيانات العرض مفقودة');
          if (!schemaData.brand) issues.push('❌ العلامة التجارية مفقودة');
          if (!schemaData.url) issues.push('❌ URL المنتج مفقود');
          
          // فحص URL
          if (schemaData.url && schemaData.url.includes('-0')) {
            issues.push('⚠️ URL يحتوي على "-0" بدلاً من slug صحيح');
          }
          
          if (issues.length === 0) {
            console.log('✅ Schema يبدو صحيحاً!');
          } else {
            console.log('⚠️ مشاكل محتملة:');
            issues.forEach(issue => console.log(`  ${issue}`));
          }
          
          // عرض Schema كاملاً للمراجعة
          console.log('\n📄 Schema كاملاً:');
          console.log(JSON.stringify(schemaData, null, 2));
          
        } else {
          console.log('❌ لم يتم العثور على Schema في الصفحة');
        }
        
      } catch (error) {
        console.error('❌ خطأ في تحليل Schema:', error.message);
      }
    });
    
  }).on('error', (error) => {
    console.error('❌ خطأ في جلب الصفحة:', error.message);
  });
}

// اختبار صفحة المنتج
const productUrl = 'https://droobhajer.com/ar/products/صينية-خبز-مستطيلة-متوسطة-برتقالية-RH02857';
testProductSchema(productUrl);
