import { redirect } from 'next/navigation';
import { getCategories } from '../../../../lib/mysql-database';
import { Locale } from '../../../../lib/i18n';

interface CategoryRedirectPageProps {
  params: Promise<{
    locale: string;
    categoryName: string;
  }>;
}

export default async function CategoryRedirectPage({ params }: CategoryRedirectPageProps) {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const categoryName = resolvedParams?.categoryName || '';

  try {
    // جلب جميع الفئات للبحث عن الفئة المطلوبة
    const categories = await getCategories();

    if (categories && categories.length > 0) {
      // البحث عن الفئة بالاسم (إنجليزي أو عربي)
      const category = categories.find(cat => 
        cat.name.toLowerCase().replace(/\s+/g, '-') === categoryName.toLowerCase() ||
        cat.name_ar.toLowerCase().replace(/\s+/g, '-') === categoryName.toLowerCase() ||
        cat.name.toLowerCase().replace(/\s+/g, '') === categoryName.toLowerCase() ||
        cat.name_ar.toLowerCase().replace(/\s+/g, '') === categoryName.toLowerCase() ||
        cat.name.toLowerCase() === categoryName.toLowerCase().replace(/-/g, ' ') ||
        cat.name_ar.toLowerCase() === categoryName.toLowerCase().replace(/-/g, ' ')
      );

      if (category) {
        // إعادة التوجيه إلى الرابط الصحيح مع ID
        redirect(`/${locale}/category/${category.id}`);
      }
    }

    // إذا لم يتم العثور على الفئة، توجيه إلى صفحة الفئات
    redirect(`/${locale}/categories`);
  } catch (error) {
    console.error('Error redirecting category:', error);
    // في حالة الخطأ، توجيه إلى صفحة الفئات
    redirect(`/${locale}/categories`);
  }
}
