import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Locale, locales } from '../../../../lib/i18n';
import { Category, Subcategory } from '../../../../types/mysql-database';
import { getCategories } from '../../../../lib/mysql-database';
import ResponsiveCategoryPage from '../../../../components/ResponsiveCategoryPage';

interface CategoryPageProps {
  params: Promise<{
    locale: string;
    categoryId: string;
  }>;
}

// دالة لجلب بيانات الفئة مباشرة من قاعدة البيانات
async function fetchCategoryData(categoryId: string): Promise<{
  category: Category | null;
  subcategories: Subcategory[];
}> {
  try {
    console.log('🚀 Server: Fetching category details for ID:', categoryId);

    // جلب بيانات الفئة
    const categoryResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/categories?id=${categoryId}`, {
      next: { revalidate: 900 } // 15 دقيقة
    });

    let category: Category | null = null;
    if (categoryResponse.ok) {
      const categoryResult = await categoryResponse.json();
      if (categoryResult.success && categoryResult.data) {
        category = categoryResult.data;
      }
    }

    if (!category) {
      console.log(`❌ Server: Category not found for ID: ${categoryId}`);
      return { category: null, subcategories: [] };
    }

    // جلب الفئات الفرعية
    const subcategoriesResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/subcategories?categoryId=${categoryId}`, {
      next: { revalidate: 900 }
    });

    let subcategories: Subcategory[] = [];
    if (subcategoriesResponse.ok) {
      const subcategoriesResult = await subcategoriesResponse.json();
      if (subcategoriesResult.success && subcategoriesResult.data) {
        subcategories = subcategoriesResult.data;
      }
    }

    console.log('📦 Server: Category data fetched successfully');
    return { category, subcategories };
  } catch (error) {
    console.error('❌ Server: Error fetching category details:', error);
    return { category: null, subcategories: [] };
  }
}

// دالة لإنشاء metadata للصفحة
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const categoryId = resolvedParams?.categoryId || '';

  // جلب بيانات الفئة للـ metadata
  try {
    const { category } = await fetchCategoryData(categoryId);

    if (category) {
      const title = locale === 'ar' ? category.name_ar : category.name;
      const description = locale === 'ar' ? category.description_ar : category.description;

      return {
        title: `${title} - DROOB HAJER`,
        description: description || `${locale === 'ar' ? 'تصفح منتجات' : 'Browse'} ${title}`,
        openGraph: {
          title: `${title} - DROOB HAJER`,
          description: description || `${locale === 'ar' ? 'تصفح منتجات' : 'Browse'} ${title}`,
          images: category.image_url ? [category.image_url] : [],
        },
      };
    }
  } catch (error) {
    console.error('Error fetching category metadata:', error);
  }

  return {
    title: 'فئة المنتجات - DROOB HAJER',
    description: 'تصفح فئات المنتجات في متجر دروب هاجر',
  };
}

// دالة لإنشاء static params لجميع الفئات
export async function generateStaticParams(): Promise<{ locale: string; categoryId: string }[]> {
  try {
    console.log('🔧 Generating static params for categories...');

    // جلب جميع الفئات النشطة
    const categories = await getCategories();
    const activeCategories = categories.filter(category => category.is_active && !category.deleted_at);

    const params: { locale: string; categoryId: string }[] = [];

    for (const category of activeCategories) {
      // إنشاء params لكل لغة
      for (const locale of locales) {
        params.push({
          locale,
          categoryId: category.id.toString()
        });
      }
    }

    console.log(`✅ Generated ${params.length} static params for categories`);
    return params;
  } catch (error) {
    console.error('❌ Error generating static params for categories:', error);
    return []; // إرجاع مصفوفة فارغة في حالة الخطأ
  }
}

// إعدادات ISR للصفحة
export const revalidate = 900; // 15 دقيقة
export const dynamic = 'force-static';

export default async function CategoryPage({ params }: CategoryPageProps) {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const categoryId = resolvedParams?.categoryId || '';

  // جلب البيانات من الخادم
  const { category, subcategories } = await fetchCategoryData(categoryId);

  // إذا لم يتم العثور على الفئة، إظهار صفحة 404
  if (!category) {
    console.error('❌ Category not found for ID:', categoryId);
    notFound();
  }

  return (
    <div lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} className={`${locale === 'ar' ? 'rtl font-tajawal' : 'ltr font-inter'} min-h-screen`}>
      <ResponsiveCategoryPage
        locale={locale}
        categoryId={categoryId}
        initialCategory={category}
        initialSubcategories={subcategories}
      />
    </div>
  );
}
