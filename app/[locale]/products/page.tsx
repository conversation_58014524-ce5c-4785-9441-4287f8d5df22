import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { getPageSEO } from '../../../lib/seo.config';
import { getProductsWithDetails, getCategories } from '../../../lib/mysql-database';
import { ProductWithDetails, Category } from '../../../types/mysql-database';
import ResponsiveProductsPage from '../../../components/ResponsiveProductsPage';


// إنشاء metadata لصفحة المنتجات
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  const seoData = getPageSEO(locale, 'products');
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return {
    title: seoData.title,
    description: seoData.description,
    keywords: seoData.additionalMetaTags?.find(tag => tag.name === 'keywords')?.content,

    openGraph: {
      title: seoData.title,
      description: seoData.description,
      url: `${baseUrl}/${locale}/products`,
      siteName: locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER',
      images: [
        {
          url: `${baseUrl}/images/og-products.jpg`,
          width: 1200,
          height: 630,
          alt: seoData.title,
        },
      ],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },

    twitter: {
      card: 'summary_large_image',
      title: seoData.title,
      description: seoData.description,
      images: [`${baseUrl}/images/og-products.jpg`],
    },

    alternates: {
      canonical: `${baseUrl}/${locale}/products`,
      languages: {
        'ar': `${baseUrl}/ar/products`,
        'en': `${baseUrl}/en/products`,
      },
    },
  };
}



// دالة لجلب البيانات من الخادم
async function fetchProductsData() {
  try {
    console.log('🚀 Server: Fetching products and categories...');

    // جلب المنتجات والفئات مباشرة من قاعدة البيانات
    const [products, categories] = await Promise.all([
      getProductsWithDetails(),
      getCategories()
    ]);

    console.log(`✅ Server: Fetched ${products.length} products and ${categories.length} categories`);

    return {
      products: products.filter((p: ProductWithDetails) => p.is_available), // فقط المنتجات المتاحة
      categories: categories.filter((c: Category) => c.is_active) // فقط الفئات النشطة
    };
  } catch (error) {
    console.error('❌ Server: Error fetching products data:', error);
    return {
      products: [],
      categories: []
    };
  }
}

export default async function ProductsPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  // جلب البيانات من الخادم
  const { products, categories } = await fetchProductsData();

  return (
    <div lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} className={`${locale === 'ar' ? 'rtl font-tajawal' : 'ltr font-inter'} min-h-screen`}>
      <ResponsiveProductsPage
        locale={locale}
        initialProducts={products}
        initialCategories={categories}
      />
    </div>
  );
}
