import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { Locale } from '../../../lib/i18n';

interface SearchPageProps {
  params: Promise<{
    locale: string;
  }>;
  searchParams: Promise<{
    q?: string;
    query?: string;
    search?: string;
  }>;
}

// إنشاء metadata لصفحة البحث
export async function generateMetadata({
  params,
  searchParams
}: SearchPageProps): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;
  const { q, query, search } = await searchParams;
  const searchQuery = q || query || search || '';

  const title = locale === 'ar' 
    ? `البحث${searchQuery ? ` عن "${searchQuery}"` : ''} - دروب هاجر`
    : `Search${searchQuery ? ` for "${searchQuery}"` : ''} - DROOB HAJER`;

  const description = locale === 'ar'
    ? `ابحث عن المنتجات في متجر دروب هاجر${searchQuery ? ` - نتائج البحث عن "${searchQuery}"` : ''}`
    : `Search for products at DROOB HAJER${searchQuery ? ` - Search results for "${searchQuery}"` : ''}`;

  return {
    title,
    description,
    robots: 'index, follow',
  };
}

export default async function SearchPage({
  params,
  searchParams
}: SearchPageProps) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;
  const { q, query, search } = await searchParams;
  
  // الحصول على استعلام البحث من أي من المعاملات
  const searchQuery = q || query || search || '';
  
  // إعادة توجيه إلى صفحة المنتجات مع معامل البحث
  const redirectUrl = searchQuery 
    ? `/${locale}/products?search=${encodeURIComponent(searchQuery)}`
    : `/${locale}/products`;
    
  redirect(redirectUrl);
}
