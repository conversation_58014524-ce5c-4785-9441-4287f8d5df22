import { Metadata } from 'next';
import Link from 'next/link';
import { Locale } from '../../../lib/i18n';
import { getCategories, getSubcategories } from '../../../lib/mysql-database';
import { Category, Subcategory } from '../../../types/mysql-database';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';

interface SitemapPageProps {
  params: Promise<{
    locale: string;
  }>;
}

export async function generateMetadata({ params }: SitemapPageProps): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  const title = locale === 'ar' ? 'خريطة الموقع - DROOB HAJER' : 'Sitemap - DROOB HAJER';
  const description = locale === 'ar' 
    ? 'تصفح جميع صفحات موقع دروب هاجر للمعدات والأدوات'
    : 'Browse all pages of DROOB HAJER equipment and tools website';

  return {
    title,
    description,
    alternates: {
      canonical: `${baseUrl}/${locale}/sitemap`,
      languages: {
        'ar': `${baseUrl}/ar/sitemap`,
        'en': `${baseUrl}/en/sitemap`,
      },
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function SitemapPage({ params }: SitemapPageProps) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  // جلب البيانات
  let categories: Category[] = [];
  let subcategories: Subcategory[] = [];

  try {
    [categories, subcategories] = await Promise.all([
      getCategories(),
      getSubcategories()
    ]);
  } catch (error) {
    console.error('Error fetching sitemap data:', error);
  }

  const mainPages = [
    { name: locale === 'ar' ? 'الرئيسية' : 'Home', url: `/${locale}` },
    { name: locale === 'ar' ? 'المنتجات' : 'Products', url: `/${locale}/products` },
    { name: locale === 'ar' ? 'الفئات' : 'Categories', url: `/${locale}/categories` },
    { name: locale === 'ar' ? 'من نحن' : 'About Us', url: `/${locale}/about` },
    { name: locale === 'ar' ? 'اتصل بنا' : 'Contact Us', url: `/${locale}/contact` },
    { name: locale === 'ar' ? 'السلة' : 'Cart', url: `/${locale}/cart` },
  ];

  const legalPages = [
    { name: locale === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy', url: `/${locale}/privacy` },
    { name: locale === 'ar' ? 'شروط الاستخدام' : 'Terms of Use', url: `/${locale}/terms` },
  ];

  return (
    <div lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} className={`${locale === 'ar' ? 'rtl font-tajawal' : 'ltr font-inter'} min-h-screen`}>
      <Navbar locale={locale} />
      
      <main className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4 max-w-6xl">
          <div className="bg-white rounded-lg shadow-md p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              {locale === 'ar' ? 'خريطة الموقع' : 'Sitemap'}
            </h1>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* الصفحات الرئيسية */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <i className="ri-home-line text-primary mr-2"></i>
                  {locale === 'ar' ? 'الصفحات الرئيسية' : 'Main Pages'}
                </h2>
                <ul className="space-y-2">
                  {mainPages.map((page, index) => (
                    <li key={index}>
                      <Link
                        href={page.url}
                        className="text-gray-600 hover:text-primary transition-colors duration-200 flex items-center"
                      >
                        <i className="ri-arrow-right-s-line text-primary mr-1"></i>
                        {page.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* الفئات */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <i className="ri-folder-line text-primary mr-2"></i>
                  {locale === 'ar' ? 'الفئات' : 'Categories'}
                </h2>
                <ul className="space-y-2 max-h-64 overflow-y-auto">
                  {categories.filter(cat => cat.is_active).map((category) => (
                    <li key={category.id}>
                      <Link
                        href={`/${locale}/category/${category.id}`}
                        className="text-gray-600 hover:text-primary transition-colors duration-200 flex items-center"
                      >
                        <i className="ri-arrow-right-s-line text-primary mr-1"></i>
                        {locale === 'ar' ? category.name_ar : category.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* الفئات الفرعية */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <i className="ri-folder-2-line text-primary mr-2"></i>
                  {locale === 'ar' ? 'الفئات الفرعية' : 'Subcategories'}
                </h2>
                <ul className="space-y-2 max-h-64 overflow-y-auto">
                  {subcategories.filter(sub => sub.is_active).map((subcategory) => (
                    <li key={subcategory.id}>
                      <Link
                        href={`/${locale}/subcategory/${subcategory.id}`}
                        className="text-gray-600 hover:text-primary transition-colors duration-200 flex items-center"
                      >
                        <i className="ri-arrow-right-s-line text-primary mr-1"></i>
                        {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* الصفحات القانونية */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <i className="ri-file-text-line text-primary mr-2"></i>
                  {locale === 'ar' ? 'الصفحات القانونية' : 'Legal Pages'}
                </h2>
                <ul className="space-y-2">
                  {legalPages.map((page, index) => (
                    <li key={index}>
                      <Link
                        href={page.url}
                        className="text-gray-600 hover:text-primary transition-colors duration-200 flex items-center"
                      >
                        <i className="ri-arrow-right-s-line text-primary mr-1"></i>
                        {page.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* روابط مفيدة */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <i className="ri-links-line text-primary mr-2"></i>
                  {locale === 'ar' ? 'روابط مفيدة' : 'Useful Links'}
                </h2>
                <ul className="space-y-2">
                  <li>
                    <a
                      href="/sitemap.xml"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-600 hover:text-primary transition-colors duration-200 flex items-center"
                    >
                      <i className="ri-arrow-right-s-line text-primary mr-1"></i>
                      {locale === 'ar' ? 'خريطة الموقع XML' : 'XML Sitemap'}
                    </a>
                  </li>
                  <li>
                    <a
                      href="/robots.txt"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-600 hover:text-primary transition-colors duration-200 flex items-center"
                    >
                      <i className="ri-arrow-right-s-line text-primary mr-1"></i>
                      {locale === 'ar' ? 'ملف Robots.txt' : 'Robots.txt File'}
                    </a>
                  </li>
                </ul>
              </div>

              {/* معلومات إضافية */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <i className="ri-information-line text-primary mr-2"></i>
                  {locale === 'ar' ? 'معلومات إضافية' : 'Additional Info'}
                </h2>
                <div className="text-sm text-gray-600 space-y-2">
                  <p>
                    {locale === 'ar' 
                      ? `إجمالي الفئات: ${categories.filter(cat => cat.is_active).length}`
                      : `Total Categories: ${categories.filter(cat => cat.is_active).length}`
                    }
                  </p>
                  <p>
                    {locale === 'ar' 
                      ? `إجمالي الفئات الفرعية: ${subcategories.filter(sub => sub.is_active).length}`
                      : `Total Subcategories: ${subcategories.filter(sub => sub.is_active).length}`
                    }
                  </p>
                  <p className="text-xs text-gray-500 mt-4">
                    {locale === 'ar' 
                      ? 'آخر تحديث: ديسمبر 2024'
                      : 'Last updated: December 2024'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* ملاحظة */}
            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-700">
                <i className="ri-information-line mr-2"></i>
                {locale === 'ar' 
                  ? 'هذه الصفحة تحتوي على جميع الروابط المهمة في الموقع لمساعدتك في التنقل بسهولة.'
                  : 'This page contains all important links on the website to help you navigate easily.'
                }
              </p>
            </div>
          </div>
        </div>
      </main>

      <Footer locale={locale} />
    </div>
  );
}
