import { Metadata } from 'next';
import { Locale, locales } from '../../../../lib/i18n';
import { ProductWithDetails, Category, Subcategory } from '../../../../types/mysql-database';
import { getSubcategories } from '../../../../lib/mysql-database';
import ResponsiveSubcategoryPage from '../../../../components/ResponsiveSubcategoryPage';

interface PageProps {
  params: Promise<{
    locale: string;
    subcategoryId: string;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale, subcategoryId } = await params;

  // جلب بيانات الفئة الفرعية للـ metadata
  try {
    const subcategoryResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/subcategories?id=${subcategoryId}`);
    if (subcategoryResponse.ok) {
      const subcategoryResult = await subcategoryResponse.json();
      if (subcategoryResult.success && subcategoryResult.data) {
        const subcategory = subcategoryResult.data;
        const title = locale === 'ar' ? subcategory.name_ar : subcategory.name;
        const description = locale === 'ar' ? subcategory.description_ar : subcategory.description;

        return {
          title: `${title} - DROOB HAJER`,
          description: description || `${locale === 'ar' ? 'تصفح منتجات' : 'Browse'} ${title}`,
        };
      }
    }
  } catch (error) {
    console.error('Error fetching subcategory metadata:', error);
  }

  return {
    title: 'منتجات الفئة الفرعية - DROOB HAJER',
    description: 'تصفح منتجات الفئة الفرعية في متجر دروب هاجر',
  };
}

// دالة لإنشاء static params لجميع الفئات الفرعية
export async function generateStaticParams(): Promise<{ locale: string; subcategoryId: string }[]> {
  try {
    console.log('🔧 Generating static params for subcategories...');

    // جلب جميع الفئات الفرعية النشطة
    const subcategories = await getSubcategories();
    const activeSubcategories = subcategories.filter(subcategory => subcategory.is_active && !subcategory.deleted_at);

    const params: { locale: string; subcategoryId: string }[] = [];

    for (const subcategory of activeSubcategories) {
      // إنشاء params لكل لغة
      for (const locale of locales) {
        params.push({
          locale,
          subcategoryId: subcategory.id.toString()
        });
      }
    }

    console.log(`✅ Generated ${params.length} static params for subcategories`);
    return params;
  } catch (error) {
    console.error('❌ Error generating static params for subcategories:', error);
    return []; // إرجاع مصفوفة فارغة في حالة الخطأ
  }
}

// إعدادات ISR للصفحة
export const revalidate = 900; // 15 دقيقة
export const dynamic = 'force-static';

export default async function SubcategoryPage({ params }: PageProps) {
  const { locale: localeParam, subcategoryId } = await params;
  const locale = (localeParam || 'ar') as Locale;

  // جلب البيانات للصفحة
  let products: ProductWithDetails[] = [];
  let subcategory: Subcategory | null = null;
  let category: Category | null = null;

  try {
    // جلب بيانات الفئة الفرعية
    const subcategoryResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/subcategories?id=${subcategoryId}`, {
      next: { revalidate: 900 } // 15 دقيقة
    });

    if (subcategoryResponse.ok) {
      const subcategoryResult = await subcategoryResponse.json();
      if (subcategoryResult.success && subcategoryResult.data) {
        subcategory = subcategoryResult.data;

        // جلب بيانات الفئة الرئيسية
        if (subcategory && subcategory.category_id) {
          const categoryResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/categories?id=${subcategory.category_id}`, {
            next: { revalidate: 900 }
          });

          if (categoryResponse.ok) {
            const categoryResult = await categoryResponse.json();
            if (categoryResult.success && categoryResult.data) {
              category = categoryResult.data;
            }
          }
        }

        // جلب المنتجات (الصفحة الأولى فقط - 12 منتج)
        const productsResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/products?subcategoryId=${subcategoryId}&page=1&limit=12`, {
          next: { revalidate: 900 }
        });

        if (productsResponse.ok) {
          const productsResult = await productsResponse.json();
          if (productsResult.success && productsResult.data) {
            products = productsResult.data;
          }
        }
      }
    }
  } catch (error) {
    console.error('Error fetching subcategory data:', error);
  }

  return (
    <ResponsiveSubcategoryPage
      locale={locale}
      subcategoryId={subcategoryId}
      initialProducts={products}
      initialSubcategory={subcategory || undefined}
      initialCategory={category || undefined}
    />
  );
}