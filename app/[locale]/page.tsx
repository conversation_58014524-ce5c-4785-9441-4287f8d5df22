import { Metadata } from 'next';
import { Locale } from '../../lib/i18n';
import { getPageSEO } from '../../lib/seo.config';
import { getProductsWithDetails, getCategories } from '../../lib/mysql-database';
import ResponsiveHomePage from '../../components/ResponsiveHomePage';

import StructuredData from '../../components/SEO/StructuredData';

// إنشاء metadata للصفحة الرئيسية
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  const seoData = getPageSEO(locale, 'home');
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return {
    title: seoData.title,
    description: seoData.description,
    keywords: seoData.additionalMetaTags?.find(tag => tag.name === 'keywords')?.content,

    openGraph: {
      title: seoData.title,
      description: seoData.description,
      url: `${baseUrl}/${locale}`,
      siteName: locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER',
      images: [
        {
          url: `${baseUrl}/images/og-home.jpg`,
          width: 1200,
          height: 630,
          alt: seoData.title,
        },
      ],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },

    twitter: {
      card: 'summary_large_image',
      title: seoData.title,
      description: seoData.description,
      images: [`${baseUrl}/images/og-home.jpg`],
    },

    alternates: {
      canonical: `${baseUrl}/${locale}`,
      languages: {
        'ar': `${baseUrl}/ar`,
        'en': `${baseUrl}/en`,
      },
    },
  };
}

export default async function HomePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  // جلب البيانات للصفحة الرئيسية
  const [products, categories] = await Promise.all([
    getProductsWithDetails(),
    getCategories()
  ]);

  const featuredProducts = products.filter(p => p.is_available).slice(0, 6);
  const activeCategories = categories.filter(c => c.is_active);

  return (
    <>
      <StructuredData locale={locale} type="website" />
      <StructuredData locale={locale} type="organization" />
      <ResponsiveHomePage
        locale={locale}
        categories={activeCategories}
        featuredProducts={featuredProducts}
      />
    </>
  );
}
